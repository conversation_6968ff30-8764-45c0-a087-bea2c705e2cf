# Optimistic Updates Implementation Guide

## Overview

This guide documents the optimistic updates implementation for Redux Toolkit Query (RTK Query) in the AIDA project. The goal is to improve user experience by eliminating unnecessary loading states and refetches when performing CRUD operations.

## Problem Statement

The original implementation relied heavily on `invalidatesTags` which caused:
- Full refetches of project details after file operations
- Loading states appearing after every file create/delete/update
- Poor UX with constant loading indicators
- Unnecessary network requests

## Solution: Optimistic Updates

Instead of invalidating cache tags, we now use optimistic updates that:
- Immediately update the UI with expected results
- Only revert changes if the API call fails
- Eliminate loading states for most operations
- Provide instant feedback to users

## Implementation Details

### Resources API (`src/store/api/resources/`)

#### Key Changes:
1. **Enhanced Utils** (`utils.ts`):
   - `optimisticAddResource`: Adds new resources to all relevant caches
   - `optimisticUpdateResourceList`: Updates existing resources across caches
   - `optimisticRemoveResource`: Removes resources from all relevant caches

2. **Service Updates** (`service.ts`):
   - `createResource`: Uses optimistic addition instead of invalidation
   - `updateResource`: Already had optimistic updates, kept as-is
   - `deleteResource`: Smart cache iteration to remove from all project caches

#### Delete Resource Implementation:
```typescript
// Instead of invalidating tags, we iterate through all cached queries
// and remove the resource from any cache that contains it
const state = getState() as any;
const projectDetailsQueries = state.api.queries;

Object.keys(projectDetailsQueries).forEach((queryKey) => {
  if (queryKey.startsWith('getProjectDetails(')) {
    const query = projectDetailsQueries[queryKey];
    if (query?.data?.resources?.some((r: any) => r.id === arg.id)) {
      // Remove resource from this project's cache
    }
  }
});
```

### Projects API (`src/store/api/projects/`)

#### Planned Improvements:
1. **Folder Operations**:
   - `createProjectFolder`: Optimistic addition to project details
   - `deleteProjectFolder`: Optimistic removal from project details

2. **Utils** (`utils.ts`):
   - `optimisticAddFolder`: Add folder to project cache
   - `optimisticRemoveFolder`: Remove folder from project cache

## Benefits

### Before (Invalidation-based):
```typescript
// User clicks delete file
deleteResource({ id: 'file-123' })
// → API call starts
// → invalidatesTags: [{ type: 'Projects', id: 'project-456' }]
// → Project details refetch starts
// → Loading state shows
// → User sees loading spinner
// → Project details response received
// → UI updates with file removed
```

### After (Optimistic Updates):
```typescript
// User clicks delete file
deleteResource({ id: 'file-123' })
// → File immediately disappears from UI
// → API call happens in background
// → If successful: no further action needed
// → If failed: file reappears with error message
```

## Best Practices

### 1. Always Provide Rollback
```typescript
try {
  await queryFulfilled;
} catch {
  // Revert all optimistic updates on error
  patchResults.forEach((patch) => patch.undo());
}
```

### 2. Update All Relevant Caches
```typescript
// Update global list
dispatch(resourcesService.util.updateQueryData('getResources', {}, updateFn));

// Update project details
if (resource.projectId) {
  dispatch(projectsService.util.updateQueryData('getProjectDetails', 
    { id: resource.projectId }, updateFn));
}

// Update folder details if applicable
if (resource.projectId && resource.folderId) {
  dispatch(projectsService.util.updateQueryData('getProjectFolderDetails',
    { id: resource.projectId, folderId: resource.folderId }, updateFn));
}
```

### 3. Use Utility Functions
Create reusable utility functions for common operations:
```typescript
export const optimisticAddResource = (dispatch, newResource) => {
  // Update all relevant caches in one function call
};
```

## Migration Strategy

### Phase 1: Resources ✅
- ✅ Enhanced resource utils with add/remove functions
- ✅ Updated createResource to use optimistic addition
- ✅ Updated deleteResource to use smart cache iteration
- ✅ Maintained existing updateResource optimistic updates

### Phase 2: Projects (Next)
- [ ] Implement folder operation optimistic updates
- [ ] Update createProjectFolder and deleteProjectFolder
- [ ] Test folder operations don't show loading states

### Phase 3: Notes & Sessions (Future)
- [ ] Apply similar patterns to notes operations
- [ ] Apply similar patterns to session operations
- [ ] Comprehensive testing across all modules

## Testing Checklist

When implementing optimistic updates:
- [ ] UI updates immediately on action
- [ ] No loading states for successful operations
- [ ] Failed operations revert UI changes
- [ ] Error messages display appropriately
- [ ] All related caches are updated consistently
- [ ] No stale data in any cache after operations

## Performance Benefits

1. **Reduced Network Requests**: No unnecessary refetches
2. **Faster UI**: Immediate feedback instead of loading states
3. **Better UX**: Smooth, responsive interface
4. **Reduced Server Load**: Fewer redundant API calls

## Monitoring & Debugging

Use Redux DevTools to monitor:
- Cache updates happening immediately
- Rollbacks on failed operations
- Consistency across multiple caches
- No invalidation-based refetches for optimized operations
