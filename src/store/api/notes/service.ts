import { apiService } from '..';
import { noteApiConfigs } from './configs';

import type { Note } from './types';
import type { NoteConfigParams } from './configs';

// ----------------------------------------------------------------------

export const notesService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getNotes: build.query<Note[], {}>({
      query: noteApiConfigs.getNotes,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Notes' as const, id })),
              { type: 'Notes', id: 'LIST' },
            ]
          : [{ type: 'Notes', id: 'LIST' }],
    }),
    getNotesByResource: build.query<Note[], NoteConfigParams<'getNotesByResource'>>({
      query: noteApiConfigs.getNotesByResource,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Notes' as const, id })),
              { type: 'Notes', id: 'LIST' },
            ]
          : [{ type: 'Notes', id: 'LIST' }],
    }),
    getNotesByProject: build.query<Note[], NoteConfigParams<'getNotesByProject'>>({
      query: noteApiConfigs.getNotesByProject,
      providesTags: (result) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'Notes' as const, id })),
              { type: 'Notes', id: 'LIST' },
            ]
          : [{ type: 'Notes', id: 'LIST' }],
    }),
    getNote: build.query<Note, NoteConfigParams<'getNote'>>({
      query: noteApiConfigs.getNote,
      providesTags: (result) => (result ? [{ type: 'Notes' as const, id: result.id }] : []),
    }),
    createNote: build.mutation<Note, NoteConfigParams<'createNote'>>({
      query: noteApiConfigs.createNote,
      invalidatesTags: ['Notes'],
    }),
    updateNote: build.mutation<Note, NoteConfigParams<'updateNote'>>({
      query: noteApiConfigs.updateNote,
      invalidatesTags: (result) => (result ? [{ type: 'Notes', id: result.id }] : []),
    }),
    deleteNote: build.mutation<void, NoteConfigParams<'deleteNote'>>({
      query: noteApiConfigs.deleteNote,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          notesService.util.updateQueryData('getNotes', {}, (draft) =>
            draft.filter((item) => item.id !== arg.id)
          )
        );
        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
    }),
  }),
  overrideExisting: true,
});
