import type { Draft } from 'immer';
import type { Resource } from 'src/types';
import type { ThunkDispatch } from '@reduxjs/toolkit';

import { resourcesService } from './service';
import { projectsService } from '../projects';

export const optimisticUpdateResourceForList = (
  resources: Draft<Resource[]>,
  updatedResource: Resource
) => {
  const index = resources.findIndex((item) => item.id === updatedResource.id);
  if (index === -1) return;
  resources[index] = { ...resources[index], ...updatedResource };
};

export const optimisticUpdateResourceForDetails = (
  resource: Draft<Resource>,
  updatedResource: Resource
) => ({
  ...resource,
  ...updatedResource,
});

export const optimisticUpdateResourceList = (
  dispatch: ThunkDispatch<any, any, any>,
  updatedResource: Resource
) => {
  dispatch(
    resourcesService.util.updateQueryData('getResources', {}, (draft) =>
      optimisticUpdateResourceForList(draft, updatedResource)
    )
  );
  dispatch(
    resourcesService.util.updateQueryData('getResource', { id: updatedResource.id }, (draft) =>
      optimisticUpdateResourceForDetails(draft, updatedResource)
    )
  );

  if (updatedResource.projectId) {
    dispatch(
      projectsService.util.updateQueryData(
        'getProjectDetails',
        { id: updatedResource.projectId },
        (draft) => optimisticUpdateResourceForList(draft.resources, updatedResource)
      )
    );
  }
};
