import type { Resource } from 'src/types';

import { apiService } from '..';
import { projectsService } from '../projects';
import { resourceApiConfigs } from './configs';
import { optimisticUpdateResourceList } from './utils';

import type { BaseResponse } from '../types';
import type { ResourceConfigParams } from './configs';
import type { TranscodingStatusItem, GetResourceUploadUrlResponse } from './types';

export const resourcesService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getResources: build.query<Resource[], {}>({
      query: resourceApiConfigs.getResources,
      transformResponse: (response: Resource[]) =>
        response.map((resource: Resource) => {
          const { transcription, ...rest } = resource;
          return {
            ...rest,
            transcription: transcription?.sort((a, b) => a.startTime - b.startTime),
          };
        }),
      providesTags: (result, error, arg) =>
        result
          ? [
              ...result.map(({ id }) => ({ type: 'ResourceDetails' as const, id })),
              { type: 'Resources', id: 'LIST' },
              { type: 'Resources' },
            ]
          : ['Resources'],
    }),
    getResourceUploadUrl: build.mutation<
      GetResourceUploadUrlResponse,
      ResourceConfigParams<'getResourceUploadUrl'>
    >({
      query: resourceApiConfigs.getResourceUploadUrl,
    }),
    getResource: build.query<Resource, { id: string }>({
      query: resourceApiConfigs.getResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const { data } = await queryFulfilled;
        optimisticUpdateResourceList(dispatch, data);
      },
      transformResponse: (response: Resource) => ({
        ...response,
        transcription: response.transcription?.sort((a, b) => a.startTime - b.startTime),
      }),
      providesTags: (result) => [{ type: 'ResourceDetails', id: result?.id }],
    }),
    createResource: build.mutation<Resource, ResourceConfigParams<'createResource'>>({
      query: resourceApiConfigs.createResource,
      invalidatesTags: (result) => {
        console.log('result', result);
        if (result?.projectId) {
          return [
            { type: 'Projects', id: result.projectId },
            { type: 'ProjectFolders', id: result.folderId },
          ];
        }
        return ['Resources'];
      },
    }),
    updateResource: build.mutation<Resource, ResourceConfigParams<'updateResource'>>({
      query: resourceApiConfigs.updateResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const { data } = await queryFulfilled;
        optimisticUpdateResourceList(dispatch, data);
      },
    }),
    deleteResource: build.mutation<Resource, ResourceConfigParams<'deleteResource'>>({
      query: resourceApiConfigs.deleteResource,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const patchResult = dispatch(
          resourcesService.util.updateQueryData('getResources', {}, (draft) =>
            draft.filter((item) => item.id !== arg.id)
          )
        );

        try {
          await queryFulfilled;
        } catch {
          patchResult.undo();
        }
      },
      invalidatesTags: (result) => {
        if (result?.projectId) {
          return [
            { type: 'Projects', id: result.projectId },
            { type: 'ProjectFolders', id: result.folderId },
          ];
        }
        return ['Resources'];
      },
    }),
    uploadResourceThumbnail: build.mutation<
      { url: string },
      ResourceConfigParams<'uploadResourceThumbnail'>
    >({
      query: resourceApiConfigs.uploadResourceThumbnail,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        const { data } = await queryFulfilled;

        dispatch(
          resourcesService.util.updateQueryData('getResources', {}, (draft) => {
            const index = draft.findIndex((item) => item.id === arg.id);
            if (index === -1) return;
            draft[index] = { ...draft[index], thumbnailUrl: data.url };
          })
        );
      },
    }),
    extendResourceRetention: build.mutation<
      Resource,
      ResourceConfigParams<'extendResourceRetention'>
    >({
      query: resourceApiConfigs.extendResourceRetention,
      invalidatesTags: (result) => {
        console.log('result', result);
        return ['Resources'];
      },
    }),
    transcodingStatus: build.query<
      TranscodingStatusItem[],
      ResourceConfigParams<'transcodingStatus'>
    >({
      query: resourceApiConfigs.transcodingStatus,
      transformResponse: (response: BaseResponse<TranscodingStatusItem[]>) => response.data,
      async onQueryStarted(arg, { dispatch, queryFulfilled }) {
        try {
          const { data } = await queryFulfilled;
          // Update the resources in the cache
          dispatch(
            projectsService.util.updateQueryData('getProjectDetails', { id: arg.id }, (draft) => {
              data.forEach((item) => {
                const resource = draft.resources.find(
                  (r) => r.id === item.id && r.isTranscoding !== item.isTranscoding
                );
                if (!resource) return;
                resource.isTranscoding = item.isTranscoding;
              });
            })
          );
        } catch (error) {
          console.log('error', error);
          // Error handling is managed by the polling hook
        }
      },
    }),
  }),
  overrideExisting: true,
});
