import type { Session } from 'src/types';

import { apiService } from '..';
import { sessionApiConfigs } from './configs';
import { projectsService } from '../projects';
import { resourcesService } from '../resources';

import type { SessionConfigParams } from './configs';

export const sessionsService = apiService.injectEndpoints({
  endpoints: (build) => ({
    getSessions: build.query<Session[], {}>({
      query: sessionApiConfigs.getSessions,
    }),
    getOngoingSessions: build.query<Session[], SessionConfigParams<'getOngoingSessions'>>({
      query: sessionApiConfigs.getOngoingSessions,
      async onQueryStarted(arg, { dispatch, queryFulfilled, getCacheEntry }) {
        const { data: cachedData = [] } = getCacheEntry();
        const { data } = await queryFulfilled;
        const hasCompletedSessions =
          cachedData.length > 0 &&
          cachedData.some(
            (cachedSession) => !data.find((session) => session.id === cachedSession.id)
          );

        // If any completed sessions are found, invalidate the resources cache
        if (hasCompletedSessions) {
          dispatch(resourcesService.util.invalidateTags(['Resources']));
          if (arg.projectId) {
            dispatch(
              projectsService.util.invalidateTags([{ type: 'Projects', id: arg.projectId }])
            );
          }
        }
      },
      providesTags: (result, error, arg) =>
        result
          ? [...result.map(({ id }) => ({ type: 'Sessions' as const, id })), 'Sessions']
          : ['Sessions'],
    }),
    getSession: build.query<Session, { id: string }>({
      query: sessionApiConfigs.getSession,
    }),
    deleteSession: build.mutation<Session, SessionConfigParams<'deleteSession'>>({
      query: sessionApiConfigs.deleteSession,
      invalidatesTags: (result, error, arg) => [{ type: 'Sessions', id: arg.id }],
    }),
    recordSession: build.mutation<Session, SessionConfigParams<'recordSession'>>({
      query: sessionApiConfigs.recordSession,
      invalidatesTags: ['Sessions'],
    }),
    retrySession: build.mutation<Session, SessionConfigParams<'retrySession'>>({
      query: sessionApiConfigs.retrySession,
      invalidatesTags: (result, error, arg) => [{ type: 'Sessions', id: arg.id }],
    }),
  }),
  overrideExisting: true,
});
