import type { Draft } from 'immer';
import type { ProjectFolder } from 'src/types/project';
import type { ThunkDispatch } from '@reduxjs/toolkit';

import { projectsService } from './service';

export const optimisticAddFolderToProject = (
  folders: Draft<ProjectFolder[]>,
  newFolder: ProjectFolder
) => {
  // Add to the beginning of the list (most recent first)
  folders.unshift(newFolder);
};

export const optimisticRemoveFolderFromProject = (
  folders: Draft<ProjectFolder[]>,
  folderId: string
) => {
  const index = folders.findIndex((folder) => folder.id === folderId);
  if (index !== -1) {
    folders.splice(index, 1);
  }
};

export const optimisticAddFolder = (
  dispatch: ThunkDispatch<any, any, any>,
  projectId: string,
  newFolder: ProjectFolder
) => {
  // Update project details to include the new folder
  dispatch(
    projectsService.util.updateQueryData(
      'getProjectDetails',
      { id: projectId },
      (draft) => optimisticAddFolderToProject(draft.folders, newFolder)
    )
  );
};

export const optimisticRemoveFolder = (
  dispatch: ThunkDispatch<any, any, any>,
  projectId: string,
  folderId: string
) => {
  // Update project details to remove the folder
  dispatch(
    projectsService.util.updateQueryData(
      'getProjectDetails',
      { id: projectId },
      (draft) => optimisticRemoveFolderFromProject(draft.folders, folderId)
    )
  );
};
