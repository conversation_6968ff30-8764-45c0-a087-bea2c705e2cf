import type { Resource } from 'src/types';

import { format } from 'date-fns';

import { Box, Stack, useTheme, Typography, IconButton, useMediaQuery } from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { fileFormat } from 'src/components/file-thumbnail';
import TruncateTypography from 'src/components/truncate-typography';

import ResourceActions from '../../resource-actions';
import ResourceLocation from '../../resource-card/components/resource-location';
import ResourcePreviewer from '../../resource-card/components/resource-previewer';
import ResourceThumbnail from '../../resource-card/components/resource-thumbnail';

import type { MainPanel } from '..';

interface Props {
  resource: Resource;
  onClose: () => void;
  activePanels: Record<MainPanel, boolean>;
  onTogglePanel: (panel: MainPanel) => void;
  navigationItems: {
    value: MainPanel;
    label: string;
    icon: JSX.Element;
  }[];
}

const InfoPanel: React.FC<Props> = ({
  resource,
  onClose,
  activePanels,
  onTogglePanel,
  navigationItems,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const fFormat = fileFormat(resource.fileName);

  const isAudioOrVideo = fFormat === 'audio' || fFormat === 'video';

  return (
    <>
      <Stack direction="row" justifyContent="space-between" alignItems="center">
        <TruncateTypography
          variant="subtitle1"
          sx={{ fontSize: 16 }}
          text={resource.name}
          maxLength={40}
        />
        <Stack direction="row" gap={2}>
          <ResourceActions
            resource={resource}
            sx={{ maxHeight: 20 }}
            hiddenActions={['view', 'select']}
          />
          <IconButton color="default" edge="start" onClick={onClose} sx={{ p: 0 }}>
            <Iconify icon="mingcute:close-line" />
          </IconButton>
        </Stack>
      </Stack>
      <Typography variant="caption" color="textSecondary" sx={{ mt: 1 }}>
        Creation date: {format(resource.fileLastModified, 'dd MMM yyyy hh:mm a')}
      </Typography>
      <ResourceLocation resource={resource} sx={{ mt: 1 }} />
      <Box sx={{ width: isMobile ? '100%' : 'auto', mt: 1 }}>
        {isAudioOrVideo ? (
          <ResourcePreviewer data={resource} />
        ) : (
          <ResourceThumbnail data={resource} />
        )}
      </Box>

      <Stack direction="row" spacing={4} justifyContent="center" alignItems="center" sx={{ mt: 2 }}>
        {navigationItems.map((item) => {
          const isActive = activePanels[item.value] === true;
          return (
            <Stack
              key={item.value}
              spacing={1}
              alignItems="center"
              sx={{
                cursor: 'pointer',
                color: isActive ? theme.palette.primary.main : 'text.secondary',
                '&:hover': {
                  color: theme.palette.primary.main,
                },
              }}
              onClick={() => onTogglePanel(item.value)}
            >
              <Box
                sx={{
                  width: 48,
                  height: 48,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  border: 1,
                  borderColor: isActive ? 'primary.main' : 'divider',
                  borderRadius: '50%',
                  bgcolor: isActive ? 'primary.main' : 'transparent',
                  transition: theme.transitions.create(['background-color', 'border-color']),
                  '& svg': {
                    color: isActive ? '#fff' : 'inherit',
                  },
                }}
              >
                {item.icon}
              </Box>
              <Typography variant="subtitle2">{item.label}</Typography>
            </Stack>
          );
        })}
      </Stack>
    </>
  );
};

export default InfoPanel;
