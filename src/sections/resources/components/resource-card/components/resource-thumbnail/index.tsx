import type { BoxProps } from '@mui/material';

import { Box } from '@mui/material';

import { Image } from 'src/components/image';
import { EmptyContent } from 'src/components/empty-content';
import { fileThumb, fileFormat } from 'src/components/file-thumbnail';

import ResourcePdfThumbnail from './pdf';
import ResourceVideoThumbnail from './video';

import type { ResourceItem } from '../../../resources-list';

const ThumbnailComponentsMap: Record<string, React.FC<{ data: ResourceItem }>> = {
  video: ResourceVideoThumbnail,
  // TODO: Improve audio thumbnail and enable later
  // audio: ResourceAudioThumbnail,
  pdf: ResourcePdfThumbnail,
};

export const THUMBNAIL_HEIGHT = 230;

const ResourceThumbnail: React.FC<{ data: ResourceItem } & BoxProps> = ({ data, ...props }) => {
  const format = fileFormat(data.fileName);

  const ThumbnailComponent = ThumbnailComponentsMap[format];

  // For on-ongoing session; showing placeholder
  if (data.cardType === 'session') {
    return (
      <Box sx={{ height: THUMBNAIL_HEIGHT - 5 }} {...props}>
        <EmptyContent
          title="Recording session..."
          description={data.url}
          sx={{
            p: 0,
          }}
          slotProps={{
            img: {
              sx: {
                maxWidth: 120,
              },
            },
            description: {
              sx: {
                whiteSpace: 'nowrap',
              },
            },
          }}
        />
      </Box>
    );
  }

  if (!ThumbnailComponent) {
    return (
      <Box
        sx={{
          height: THUMBNAIL_HEIGHT,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          ...props.sx,
        }}
        {...props}
      >
        <Image
          visibleByDefault
          src={fileThumb(format)}
          alt={data.name}
          sx={{
            height: '50%',
            borderRadius: 1,
            '& .minimal__image__img': { objectFit: 'contain' },
          }}
        />
      </Box>
    );
  }

  return <ThumbnailComponent data={data} />;
};

export default ResourceThumbnail;
